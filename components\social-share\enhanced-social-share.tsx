"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Share2, Facebook, Instagram, Download, Copy } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Product } from "@/utils/types";

interface EnhancedSocialShareProps {
  product: Product;
  effectivePrice: number;
  isOnSale: boolean;
}

export default function EnhancedSocialShare({ 
  product, 
  effectivePrice, 
  isOnSale 
}: EnhancedSocialShareProps) {
  const { toast } = useToast();
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  const formatPrice = (price: number) => `M ${price.toFixed(2)}`;

  // Generate a shareable image with detailed product info
  const generateShareableImage = async (): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;

      // Set canvas size for social media (1200x800 for more content)
      canvas.width = 1200;
      canvas.height = 800;

      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#f8fafc');
      gradient.addColorStop(1, '#e2e8f0');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Load product image first
      const img = new Image();
      img.crossOrigin = 'anonymous';

      // Load RIVV logo
      const logo = new Image();
      logo.crossOrigin = 'anonymous';

      let imagesLoaded = 0;
      const totalImages = 2;

      const checkAllImagesLoaded = () => {
        imagesLoaded++;
        if (imagesLoaded === totalImages) {
          drawCanvas();
        }
      };

      const drawCanvas = () => {
        // Draw product image (left side)
        const imgSize = 400;
        const imgX = 50;
        const imgY = (canvas.height - imgSize) / 2;

        // Create rounded rectangle for image
        ctx.save();
        ctx.beginPath();
        ctx.roundRect(imgX, imgY, imgSize, imgSize, 20);
        ctx.clip();
        ctx.drawImage(img, imgX, imgY, imgSize, imgSize);
        ctx.restore();

        // Add white overlay for text area
        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
        ctx.beginPath();
        ctx.roundRect(500, 50, 650, 700, 20);
        ctx.fill();

        // Add RIVV logo at top right corner
        const logoSize = 80;
        const logoX = canvas.width - logoSize - 30;
        const logoY = 30;
        ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);

        // Add RIVV text brand
        ctx.fillStyle = '#1e40af';
        ctx.font = 'bold 48px Arial';
        ctx.fillText('RIVV', 540, 110);

        ctx.fillStyle = '#64748b';
        ctx.font = '24px Arial';
        ctx.fillText('Premium Sneakers', 540, 140);

        let currentY = 190;

        // Product name with proper text wrapping
        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 30px Arial';
        const maxWidth = 580; // Reduced to prevent overflow
        const words = product.name.split(' ');
        let line = '';
        let lineCount = 0;
        const maxLines = 2; // Limit to 2 lines for product name

        for (let n = 0; n < words.length && lineCount < maxLines; n++) {
          const testLine = line + words[n] + ' ';
          const metrics = ctx.measureText(testLine);
          const testWidth = metrics.width;

          if (testWidth > maxWidth && n > 0) {
            ctx.fillText(line.trim(), 540, currentY);
            line = words[n] + ' ';
            currentY += 35;
            lineCount++;
          } else {
            line = testLine;
          }
        }

        // Handle remaining text
        if (line.trim() && lineCount < maxLines) {
          // If we're on the last allowed line and there are more words, add ellipsis
          if (lineCount === maxLines - 1 && words.length > words.indexOf(line.trim().split(' ').pop()) + 1) {
            line = line.trim() + '...';
          }
          ctx.fillText(line.trim(), 540, currentY);
        }
        currentY += 50;

        // Brand
        ctx.fillStyle = '#374151';
        ctx.font = 'bold 24px Arial';
        ctx.fillText(`Brand: ${product.brand}`, 540, currentY);
        currentY += 40;

        // Price
        ctx.fillStyle = '#dc2626';
        ctx.font = 'bold 36px Arial';
        ctx.fillText(`Price: ${formatPrice(effectivePrice)}`, 540, currentY);
        currentY += 40;

        if (isOnSale) {
          ctx.fillStyle = '#9ca3af';
          ctx.font = '24px Arial';
          ctx.save();
          ctx.setLineDash([2, 2]);
          ctx.strokeStyle = '#9ca3af';
          ctx.lineWidth = 2;
          const originalPriceText = `Was: ${formatPrice(product.price)}`;
          ctx.fillText(originalPriceText, 540, currentY);

          // Draw strikethrough line
          const originalPriceWidth = ctx.measureText(originalPriceText).width;
          const strikeY = currentY - 8;
          ctx.beginPath();
          ctx.moveTo(540, strikeY);
          ctx.lineTo(540 + originalPriceWidth, strikeY);
          ctx.stroke();
          ctx.restore();
          currentY += 35;
        } else {
          currentY += 15;
        }

        // Sizes Available
        if (product.sizes && product.sizes.length > 0) {
          ctx.fillStyle = '#374151';
          ctx.font = '22px Arial';
          const sizesText = `Sizes Available: ${product.sizes.slice(0, 8).join(', ')}${product.sizes.length > 8 ? '...' : ''}`;
          ctx.fillText(sizesText, 540, currentY);
          currentY += 35;
        }

        // Stock Status
        ctx.fillStyle = product.stock > 0 ? '#059669' : '#dc2626';
        ctx.font = 'bold 24px Arial';
        ctx.fillText(`Stock: ${product.stock > 0 ? 'In Stock' : 'Out of Stock'}`, 540, currentY);
        currentY += 40;

        // Rating
        ctx.fillStyle = '#f59e0b';
        ctx.font = '22px Arial';
        ctx.fillText(`⭐ ${product.rating.toFixed(1)}/5 (${product.reviewCount} reviews)`, 540, currentY);
        currentY += 45;

        // Description with improved text wrapping
        if (product.description) {
          ctx.fillStyle = '#4b5563';
          ctx.font = '18px Arial';

          // Split description into sentences and take first two
          const sentences = product.description.split(/[.!?]+/).filter(s => s.trim().length > 0);
          const firstTwoSentences = sentences.slice(0, 2).join('. ') + (sentences.length > 0 ? '.' : '');

          // Wrap text for description with better handling
          const descWords = firstTwoSentences.split(' ');
          let descLine = '';
          const descMaxWidth = 560; // Reduced to prevent overflow
          let lineCount = 0;
          const maxLines = 3;

          for (let n = 0; n < descWords.length && lineCount < maxLines; n++) {
            const testLine = descLine + descWords[n] + ' ';
            const metrics = ctx.measureText(testLine);
            const testWidth = metrics.width;

            if (testWidth > descMaxWidth && n > 0) {
              ctx.fillText(descLine.trim(), 540, currentY);
              descLine = descWords[n] + ' ';
              currentY += 25;
              lineCount++;
            } else {
              descLine = testLine;
            }
          }

          // Handle the last line
          if (descLine.trim() && lineCount < maxLines) {
            // If we're on the last allowed line and there are more words, add ellipsis
            const remainingWords = descWords.slice(descWords.findIndex(word =>
              descLine.includes(word)) + descLine.trim().split(' ').length);

            if (lineCount === maxLines - 1 && remainingWords.length > 0) {
              // Ensure the line fits with ellipsis
              let finalLine = descLine.trim();
              while (ctx.measureText(finalLine + '...').width > descMaxWidth && finalLine.length > 0) {
                finalLine = finalLine.substring(0, finalLine.lastIndexOf(' '));
              }
              finalLine += '...';
              ctx.fillText(finalLine, 540, currentY);
            } else {
              ctx.fillText(descLine.trim(), 540, currentY);
            }
            currentY += 30;
          }
        }

        // Website URL
        ctx.fillStyle = '#6366f1';
        ctx.font = 'bold 22px Arial';
        ctx.fillText('🛒 Shop at rivvsneakers.shop', 540, currentY + 20);

        // Convert canvas to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve(url);
          }
        }, 'image/png', 0.9);
      };

      // Set up image loading
      img.onload = checkAllImagesLoaded;
      img.onerror = () => {
        // Continue without product image
        checkAllImagesLoaded();
      };

      logo.onload = checkAllImagesLoaded;
      logo.onerror = () => {
        // Continue without logo
        checkAllImagesLoaded();
      };

      // Load images
      if (product.images && product.images.length > 0) {
        img.src = product.images[0];
      } else {
        img.onerror();
      }

      // Load RIVV logo from public folder
      logo.src = '/logo.png';
    });
  };

  // Enhanced WhatsApp share with image
  const handleWhatsAppShare = async () => {
    setIsGeneratingImage(true);
    
    try {
      // Check if Web Share API is available and supports files
      if (navigator.share && navigator.canShare) {
        const imageUrl = await generateShareableImage();
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const file = new File([blob], `${product.name.replace(/[^a-zA-Z0-9]/g, '_')}.png`, { type: 'image/png' });

        const shareData = {
          title: `${product.name} - RIVV Sneakers`,
          text: `Check out this amazing product from RIVV Sneakers!\n\n${product.name}\n${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)})` : ''}\n⭐ ${product.rating.toFixed(1)}/5 rating\n${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}\n\n🛒 Shop now at rivvsneakers.shop\n\n#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`,
          files: [file]
        };

        if (navigator.canShare(shareData)) {
          await navigator.share(shareData);
          toast({
            title: "Shared successfully!",
            description: "Product shared with image attached.",
          });
          return;
        }
      }

      // Fallback: Traditional WhatsApp sharing
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;
      const message = `*Check out this amazing product from RIVV Sneakers!*

${product.name}

💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)})` : ''}
⭐ ${product.rating.toFixed(1)}/5 rating
${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}

🛒 Shop now: ${productUrl}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`;

      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
      window.open(whatsappUrl, '_blank');

      toast({
        title: "Opening WhatsApp",
        description: "Product details ready to share. For image attachment, download the image separately.",
      });

    } catch (error) {
      console.error('Error sharing:', error);
      toast({
        title: "Sharing failed",
        description: "Please try again or share manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Enhanced Facebook share with better image handling
  const handleFacebookShare = async () => {
    setIsGeneratingImage(true);

    try {
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;

      // Generate shareable image
      const imageUrl = await generateShareableImage();

      // Convert to blob for better handling
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Create a more descriptive filename
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;

      // Download the image
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(imageUrl);

      // Create detailed Facebook post text
      const message = `🔥 Check out this amazing product from RIVV Sneakers!

${product.name}
Brand: ${product.brand}
💰 Price: ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) - SALE!` : ''}
${product.sizes && product.sizes.length > 0 ? `👟 Sizes: ${product.sizes.slice(0, 6).join(', ')}${product.sizes.length > 6 ? '...' : ''}` : ''}
⭐ Rating: ${product.rating.toFixed(1)}/5 (${product.reviewCount} reviews)
📦 Stock: ${product.stock > 0 ? 'In Stock ✅' : 'Out of Stock ❌'}

🛒 Shop now: ${productUrl}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho #Sneakers #Fashion #${product.brand.replace(/\s+/g, '')}`;

      await navigator.clipboard.writeText(message);

      toast({
        title: "Ready for Facebook!",
        description: `Image "${filename}" downloaded and post text copied. Opening Facebook...`,
        duration: 4000,
      });

      // Open Facebook with a slight delay
      setTimeout(() => {
        window.open('https://web.facebook.com/profile.php?id=61577748126919', '_blank');
      }, 1500);

    } catch (error) {
      console.error('Error preparing Facebook share:', error);
      toast({
        title: "Error preparing Facebook share",
        description: "Please try downloading the image manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Enhanced Instagram share with better image handling
  const handleInstagramShare = async () => {
    setIsGeneratingImage(true);

    try {
      const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;

      // Generate shareable image
      const imageUrl = await generateShareableImage();

      // Convert to blob for better handling
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Create Instagram-optimized filename
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;

      // Download the image
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(imageUrl);

      // Create Instagram-optimized caption (shorter, more hashtags)
      const caption = `${product.name} 👟

Brand: ${product.brand}
💰 ${formatPrice(effectivePrice)}${isOnSale ? ` (was ${formatPrice(product.price)}) 🔥` : ''}
${product.sizes && product.sizes.length > 0 ? `Sizes: ${product.sizes.slice(0, 5).join(', ')}${product.sizes.length > 5 ? '...' : ''}` : ''}
⭐ ${product.rating.toFixed(1)}/5 rating
${product.stock > 0 ? '✅ In Stock' : '❌ Out of Stock'}

🛒 Shop: ${productUrl}

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho #Sneakers #Fashion #Style #Footwear #${product.brand.replace(/\s+/g, '')} #PremiumSneakers #SneakerHead #FootwearFashion #QualityShoes`;

      await navigator.clipboard.writeText(caption);

      toast({
        title: "Ready for Instagram!",
        description: `Image "${filename}" downloaded and caption copied. Opening Instagram...`,
        duration: 4000,
      });

      // Open Instagram with a slight delay
      setTimeout(() => {
        window.open('https://www.instagram.com/rivv_premium_sneakers01/', '_blank');
      }, 1500);

    } catch (error) {
      console.error('Error preparing Instagram share:', error);
      toast({
        title: "Error preparing Instagram share",
        description: "Please try downloading the image manually.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Download shareable image with enhanced handling
  const handleDownloadImage = async () => {
    setIsGeneratingImage(true);

    try {
      const imageUrl = await generateShareableImage();

      // Convert to blob for better handling
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Create descriptive filename
      const timestamp = new Date().toISOString().slice(0, 10);
      const cleanName = product.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const filename = `RIVV_${cleanName}_${timestamp}.png`;

      // Download the image
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(imageUrl);

      toast({
        title: "Image downloaded!",
        description: `"${filename}" saved. Ready to share on any platform!`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error downloading image:', error);
      toast({
        title: "Download failed",
        description: "Please try again or check your browser settings.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingImage(false);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-2">
      <Button
        variant="outline"
        size="sm"
        className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700 hover:text-green-800 transition-all duration-200 hover:shadow-md"
        onClick={handleWhatsAppShare}
        disabled={isGeneratingImage}
      >
        {isGeneratingImage ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-1"></div>
        ) : (
          <Share2 className="h-4 w-4 mr-1" />
        )}
        WhatsApp
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700 hover:text-blue-800 transition-all duration-200 hover:shadow-md"
        onClick={handleFacebookShare}
        disabled={isGeneratingImage}
      >
        {isGeneratingImage ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-1"></div>
        ) : (
          <Facebook className="h-4 w-4 mr-1" />
        )}
        Facebook
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="bg-pink-50 hover:bg-pink-100 border-pink-300 text-pink-700 hover:text-pink-800 transition-all duration-200 hover:shadow-md"
        onClick={handleInstagramShare}
        disabled={isGeneratingImage}
      >
        {isGeneratingImage ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-pink-600 mr-1"></div>
        ) : (
          <Instagram className="h-4 w-4 mr-1" />
        )}
        Instagram
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="bg-gray-50 hover:bg-gray-100 border-gray-300 text-gray-700 hover:text-gray-800 transition-all duration-200 hover:shadow-md"
        onClick={handleDownloadImage}
        disabled={isGeneratingImage}
      >
        {isGeneratingImage ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-1"></div>
        ) : (
          <Download className="h-4 w-4 mr-1" />
        )}
        Download
      </Button>
    </div>
  );
}
